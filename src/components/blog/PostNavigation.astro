---
import type { CollectionEntry } from "astro:content";
import { Icon } from "astro-icon/components";

interface Props {
	prevPost: CollectionEntry<"post"> | null;
	nextPost: CollectionEntry<"post"> | null;
}

const { prevPost, nextPost } = Astro.props;
---

{(prevPost || nextPost) && (
	<nav class="mt-16 border-t border-zinc-200 pt-8 dark:border-zinc-700" aria-label="文章导航">
		<div class="flex flex-col gap-4 sm:flex-row sm:justify-between">
			{prevPost ? (
				<a 
					href={`/posts/${prevPost.id}/`}
					class="group flex flex-1 items-center gap-3 rounded-lg border border-zinc-200 p-4 transition-all hover:border-accent hover:bg-accent/5 dark:border-zinc-700 dark:hover:border-accent dark:hover:bg-accent/10"
				>
					<div class="flex h-8 w-8 items-center justify-center rounded-full bg-zinc-100 transition-colors group-hover:bg-accent/20 dark:bg-zinc-800">
						<Icon 
							name="mdi:chevron-left" 
							class="h-5 w-5 text-zinc-600 transition-colors group-hover:text-accent dark:text-zinc-400" 
						/>
					</div>
					<div class="flex-1 text-left">
						<div class="text-xs text-zinc-500 dark:text-zinc-400">上一篇</div>
						<div class="line-clamp-2 font-medium text-zinc-900 transition-colors group-hover:text-accent dark:text-zinc-100">
							{prevPost.data.title}
						</div>
					</div>
				</a>
			) : (
				<div class="flex-1"></div>
			)}

			{nextPost ? (
				<a 
					href={`/posts/${nextPost.id}/`}
					class="group flex flex-1 items-center gap-3 rounded-lg border border-zinc-200 p-4 transition-all hover:border-accent hover:bg-accent/5 dark:border-zinc-700 dark:hover:border-accent dark:hover:bg-accent/10"
				>
					<div class="flex-1 text-right">
						<div class="text-xs text-zinc-500 dark:text-zinc-400">下一篇</div>
						<div class="line-clamp-2 font-medium text-zinc-900 transition-colors group-hover:text-accent dark:text-zinc-100">
							{nextPost.data.title}
						</div>
					</div>
					<div class="flex h-8 w-8 items-center justify-center rounded-full bg-zinc-100 transition-colors group-hover:bg-accent/20 dark:bg-zinc-800">
						<Icon 
							name="mdi:chevron-right" 
							class="h-5 w-5 text-zinc-600 transition-colors group-hover:text-accent dark:text-zinc-400" 
						/>
					</div>
				</a>
			) : (
				<div class="flex-1"></div>
			)}
		</div>
	</nav>
)}

<style>
	/* 确保在小屏幕上也有良好的显示效果 */
	@media (max-width: 640px) {
		.line-clamp-2 {
			display: -webkit-box;
			-webkit-line-clamp: 2;
			-webkit-box-orient: vertical;
			overflow: hidden;
		}
	}
	
	/* 为减少动画偏好的用户优化 */
	@media (prefers-reduced-motion: reduce) {
		.group {
			transition: none;
		}
		
		.group * {
			transition: none;
		}
	}
</style>
